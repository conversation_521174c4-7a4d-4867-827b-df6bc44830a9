<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">

    <title>AutoSpace</title>
</head>
<body>
    <div class="user-selection">
        <ol>
            <li class = "user-selection" id = "U00001">user_1</li>
            <li class = "user-selection" id = "U00002">user_2</li>
            <li class = "user-selection" id = "U00003">user_3</li>
            <li class = "user-selection" id = "U00004">user_4</li>
        </ol>
    </div>
    <button class = 'run-button'>
        RUN
    </button>
    <div class="sessions-container">
        <div class= 'cards-container running-sessions'>Active Sessions</div>
        <div class= 'cards-container finished-sessions' style="background-color: chartreuse;">Done Sessions</div>

    </div>
        <label for="fileInput" class="file-label">📂 Upload File</label>
        <input type="file" id="fileInput" accept=".txt">

    <script>

        // directly after opening the page we need to update it with running and done sessions
        async function getAllSessions(){
                // first we need to clear the containers to avoid duplications                
                runningCardsContainer.innerHTML = ''
                finishedCardsContainer.innerHTML = ''
                
                // sending request to get all sessions                                
                const response = await fetch(`/sessions-all/${currentUserId}`)
                const data = await response.json();
                const finished_sessions = data.Done
                const running_sessions = data.Running
                
                // creating cards for running sessions                 
                Object.keys(running_sessions).forEach(session_id => {
                    
                    const runCardReload = createCard(session_id, currentUserId, running_sessions[session_id].progress, running_sessions[session_id].timestamp)                    
                    runningCardsContainer.appendChild(runCardReload)
                });
                 
                finished_sessions.forEach((session_data)=> {                
                const doneCardReload = createDoneCard(session_data[0], session_data[1])                
                finishedCardsContainer.appendChild(doneCardReload)
                })
            } 

        
        async function getToolsData(){
            const response = await fetch('/tools-data')
            const data = await response.json();
            console.log(data)
        }
        // function to run the tool and assigning done with request is back        
        async function runTool(session_id){                
                if (inputFile == null){
                    alert("Please upload a file")
                    return
                }

                const formData = new FormData();

                formData.append('file', inputFile);
                // sending request to run the tool and get the progress back                
                const response = await fetch(`/session-run/${session_id}-${currentUserId}`
                ,{method: 'POST', body: formData})

                const data = await response.json();
                inputFile = null
                return true
            }

        function createCard(currentSessionId, currentUserId, progress, currentTime){            
            const currentRunCard = document.createElement('div')
            
            currentRunCard.className = `session-${currentSessionId} tool-card`
            currentRunCard.classList.add('session-card')
            currentRunCard.id = currentSessionId
            currentRunCard.innerHTML = `<div class="title">Session #${currentSessionId}</div>`
            
            const refresh_button = create_refresh_button(currentSessionId)
            const kill_button = create_kill_button(currentSessionId)
            const progressCounter = create_progress_counter(progress)            

            const time = create_time_label(currentTime)

            currentRunCard.appendChild(time)            
            currentRunCard.appendChild(refresh_button)
            currentRunCard.appendChild(kill_button)
            currentRunCard.appendChild(progressCounter)
            
            refresh_button.addEventListener('click', async () => {
                const response = await fetch(`/session-refresh/${currentSessionId}-${currentUserId}`)
                const data = await response.json()
                progressCounter.textContent = `${data.progress}%`
                if (data.status == "Done"){
                    move_card_to_done(currentRunCard, data.status)                    
                }
            })

            kill_button.addEventListener('click', async () => {
                const response = await fetch(`/session-kill/${currentSessionId}-${currentUserId}`)
                const data = await response.json()
                move_card_to_done(currentRunCard, "Killed")
            })
                        
            return currentRunCard
        }


        function createDoneCard(currentSessionId, currentTime){

            const currentDoneCard = document.createElement('div')
            currentDoneCard.className = `done-session-${currentSessionId} tool-card`
            currentDoneCard.classList.add('done-session')
            currentDoneCard.innerHTML = `<div class="title">Session #${currentSessionId}</div>`
            const time = create_time_label(currentTime)
            currentDoneCard.appendChild(time)
            return currentDoneCard
        }

        function create_refresh_button(session_id){
            // creating refresh button that tracks the session progress
            const currentRefreshButton = document.createElement('button')
            currentRefreshButton.className = `refresh-button`
            currentRefreshButton.dataset.sessionId = `${session_id}`
            currentRefreshButton.innerHTML = "REFRESH"
            return currentRefreshButton
        }

        function create_kill_button(session_id){
            // creating kill button that kills the session
            const currentKillButton = document.createElement('button')
            currentKillButton.className = `kill-button`
            currentKillButton.dataset.sessionId = `${session_id}`
            currentKillButton.innerHTML = "KILL"
            return currentKillButton
        }

        function create_progress_counter(progress){            
            const progressCounter = document.createElement('span')
            progressCounter.className = `progress-counter`
            progressCounter.innerHTML = `${progress}%`            
            return progressCounter
        }


        function move_card_to_done(currentRunCard, status){
            
            const refresh_button = currentRunCard.querySelector('.refresh-button')
            const kill_button = currentRunCard.querySelector('.kill-button')
            const progressCounter = currentRunCard.querySelector('.progress-counter')
            
            const time = create_time_label()
            currentRunCard.appendChild(time)            

            progressCounter.textContent = `${status}`            
            currentRunCard.classList.remove('session-card')
            currentRunCard.classList.add('done-session')

            currentRunCard.removeChild(refresh_button)
            currentRunCard.removeChild(kill_button)
            finishedCardsContainer.appendChild(currentRunCard)            
        }

        function create_time_label(currentTime){

            const time = document.createElement('span')

            time.className = `time`
            if (currentTime == null || currentTime == undefined){
                time.innerHTML = getCurrentTimeHHMM()                
            }
            else{
                time.innerHTML = currentTime                
            }
            return time
        }

        function getCurrentTimeHHMM() {
            const now = new Date();
            const hours = String(now.getHours()).padStart(2, "0");   // ensure 2 digits
            const minutes = String(now.getMinutes()).padStart(2, "0"); // ensure 2 digits
            return `${hours}:${minutes}`;
        }

        // declaring the elements we will use
        let currentUserId = "U00001" 
        const runButton = document.querySelector(".run-button")
        const runningCardsContainer= document.querySelector(".running-sessions")    
        const finishedCardsContainer = document.querySelector(".finished-sessions")
        const userList = document.querySelectorAll(".user-selection li")
        const fileInput = document.getElementById('fileInput');
        let inputFile = null
        
        getToolsData()
        
        // adding event listener to user list to change the current user
        userList.forEach((user)=>{
            user.addEventListener('click', ()=>{
                currentUserId = user.id
                getAllSessions()
            })
        })

        fileInput.addEventListener('change', (event)=> {
            inputFile = event.target.files[0]
        });

        // load all sessions [finished and running] to see realtime update
        getAllSessions()

        // adding the functionality for the runButton
        runButton.addEventListener('click', async ()=>{
            // creating a random number for session id
            let session_id = Math.floor(100000 + Math.random() * 900000);
            session_id = "S"+session_id

            // before creating the card we send request to run tools and make sure we get the response to create the card
            const session_status  = await runTool(session_id)

            if (session_status){
            // creating the session card and assigning the session id
            const runCard = createCard(session_id, currentUserId, 0, null)            
            
            runningCardsContainer.appendChild(runCard)}

        })
    </script>
</body>
</html>