/* Variables */
:root {
  /* Neutral light theme */
  --color-bg: #f7f8fa;
  --color-surface: #ffffff;
  --color-text: #0f172a;              /* slate-900 */
  --color-text-secondary: #475569;     /* slate-600 */
  --color-border: #e5e7eb;             /* gray-200 */
  --color-primary: #2563eb;            /* blue-600 */
  --color-primary-hover: #1d4ed8;      /* blue-700 */
  --color-ring: #93c5fd;               /* blue-300 */

  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 24px;
  --spacing-xxl: 40px;

  --font-size-sm: 12px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 20px;

  --radius-sm: 10px;
  --radius-md: 14px;
  --radius-lg: 18px;

  --shadow-sm: 0 2px 8px rgba(15, 23, 42, 0.06);
  --shadow-md: 0 6px 20px rgba(15, 23, 42, 0.08);
  --shadow-lg: 0 14px 40px rgba(15, 23, 42, 0.12);

  --transition-fast: 160ms ease;
  --transition-med: 220ms cubic-bezier(.2,.8,.2,1);
}

/* Base */
* { box-sizing: border-box; }
html, body { height: 100%; }
body {
  margin: 0;
  background: var(--color-bg);
  color: var(--color-text);
  font: 14px/1.55 ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, "Helvetica Neue", Arial, "Noto Sans", "Apple Color Emoji", "Segoe UI Emoji";
  overflow-x: hidden;
}

/* Top Bar */
.top-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-xl);
  z-index: 1000;
  box-shadow: var(--shadow-sm);
}

.top-bar-left .logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--color-primary);
}

.top-bar-center .nav-tabs {
  display: flex;
  gap: var(--spacing-lg);
}

.nav-tab {
  padding: var(--spacing-sm) var(--spacing-lg);
  text-decoration: none;
  color: var(--color-text-secondary);
  font-weight: 500;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.nav-tab:hover {
  color: var(--color-text);
  background: #f8fafc;
}

.nav-tab.active {
  color: var(--color-primary);
  background: #eff6ff;
}

.top-bar-right .icon-group {
  display: flex;
  gap: var(--spacing-md);
}

.icon-button {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.icon-button:hover {
  background: #f8fafc;
  color: var(--color-text);
}

.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background: #ef4444;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 999px;
  min-width: 16px;
  text-align: center;
}

/* Main Layout */
.main-layout {
  display: flex;
  margin-top: 60px;
  height: calc(100vh - 60px);
}

/* Sidebar */
.sidebar {
  width: 300px;
  background: var(--color-surface);
  border-right: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.sidebar-tabs {
  border-bottom: 1px solid var(--color-border);
  padding: var(--spacing-lg);
}

.sidebar-tab {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--color-text-secondary);
  font-weight: 500;
}

.sidebar-tab:hover {
  background: #f8fafc;
  color: var(--color-text);
}

.sidebar-tab.active {
  background: var(--color-primary);
  color: white;
}

.sidebar-tab i {
  font-size: var(--font-size-lg);
}

.tab-content {
  flex: 1;
  padding: var(--spacing-xl);
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

.tab-pane h3 {
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--color-text);
  font-size: var(--font-size-xl);
  font-weight: 600;
}

/* Main Content */
.main-content {
  flex: 1;
  padding: var(--spacing-xl);
  background: radial-gradient(1200px 800px at 50% -200px, #eef2ff 0%, transparent 55%) var(--color-bg);
  overflow-y: auto;
}

/* User Selection */
.user-selection {
  margin-bottom: var(--spacing-xl);
}

.user-selection ol {
  list-style: none;
  padding: 0;
  margin: 0;
}

.user-selection li {
  padding: var(--spacing-md) var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-weight: 500;
}

.user-selection li:hover {
  background: #f8fafc;
  border-color: var(--color-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.user-selection li:active {
  background: var(--color-primary);
  color: white;
}

/* File Upload Section */
.file-upload-section {
  margin-bottom: var(--spacing-xl);
}

#fileInput {
  display: none;
}

.file-label {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background: #10b981;
  color: white;
  font-size: var(--font-size-md);
  font-weight: 600;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  border: none;
  box-shadow: var(--shadow-sm);
}

.file-label:hover {
  background: #059669;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Run Button */
.run-button {
  appearance: none;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  background: linear-gradient(180deg, var(--color-primary), var(--color-primary-hover));
  color: #fff;
  padding: 12px 18px;
  font-weight: 600;
  letter-spacing: .2px;
  box-shadow: var(--shadow-sm);
  cursor: pointer;
  transition: transform var(--transition-fast), box-shadow var(--transition-fast), filter var(--transition-fast);
  width: 100%;
  margin-top: var(--spacing-lg);
}
.run-button:hover { filter: brightness(1.03); box-shadow: var(--shadow-md); transform: translateY(-1px); }
.run-button:active { transform: translateY(0); }
.run-button:focus-visible {
  outline: none;
  box-shadow: 0 0 0 4px var(--color-ring);
}

/* Sessions Container */
.sessions-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  height: 100%;
}

/* Cards container */
.cards-container {
  flex: 1;
  padding: var(--spacing-xl);
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: var(--spacing-xl);
  align-content: start;
  overflow-y: auto;
  position: relative;
}

.cards-container::before {
  content: attr(class);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-md) var(--spacing-xl);
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  font-weight: 600;
  color: var(--color-text);
  text-transform: capitalize;
  z-index: 1;
}

.running-sessions::before {
  content: "Active Sessions";
  background: #fef3c7;
  color: #92400e;
}

.finished-sessions::before {
  content: "Done Sessions";
  background: #d1fae5;
  color: #065f46;
}

.cards-container > * {
  margin-top: 60px;
}
/* Card */
.tool-card{
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-xl);
  display: grid;
  align-content: start;
  gap: var(--spacing-lg);
  transition: transform var(--transition-med), box-shadow var(--transition-med), border-color var(--transition-fast);
}
.tool-card:hover{
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
  border-color: #dbe3f0;
}

/* Card content */
.tool-card .title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  letter-spacing: .2px;
}
.tool-card .meta {
  color: var(--color-text-secondary);
  font-size: var(--font-size-md);
}

/* Buttons inside card */
.tool-card button,
.refresh-button {
  appearance: none;
  border: 1px solid var(--color-border);
  background: #fff;
  color: var(--color-text);
  border-radius: var(--radius-sm);
  padding: 10px 12px;
  font-weight: 600;
  cursor: pointer;
  transition: background var(--transition-fast), transform var(--transition-fast), box-shadow var(--transition-fast), border-color var(--transition-fast);
}
.tool-card button:hover,
.refresh-button:hover {
  background: #f8fafc;
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
  border-color: #dbe3f0;
}
.tool-card button:focus-visible,
.refresh-button:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px var(--color-ring);
}

/* Small badge (optional if you add one) */
.badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  background: #f1f5f9;
  border: 1px solid var(--color-border);
  border-radius: 999px;
  padding: 6px 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-layout {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid var(--color-border);
  }

  .sidebar-tabs {
    display: flex;
    overflow-x: auto;
    padding: var(--spacing-md);
  }

  .sidebar-tab {
    white-space: nowrap;
    margin-right: var(--spacing-sm);
    margin-bottom: 0;
  }

  .tab-content {
    padding: var(--spacing-lg);
  }

  .main-content {
    padding: var(--spacing-lg);
  }

  .top-bar {
    padding: 0 var(--spacing-lg);
  }

  .top-bar-center {
    display: none;
  }
}

@media (max-width: 520px) {
  .cards-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .top-bar {
    padding: 0 var(--spacing-md);
  }

  .icon-group {
    gap: var(--spacing-sm) !important;
  }
}