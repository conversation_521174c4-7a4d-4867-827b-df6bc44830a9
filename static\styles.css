/* Variables */
:root {
  /* Neutral light theme */
  --color-bg: #f7f8fa;
  --color-surface: #ffffff;
  --color-text: #0f172a;              /* slate-900 */
  --color-text-secondary: #475569;     /* slate-600 */
  --color-border: #e5e7eb;             /* gray-200 */
  --color-primary: #2563eb;            /* blue-600 */
  --color-primary-hover: #1d4ed8;      /* blue-700 */
  --color-ring: #93c5fd;               /* blue-300 */

  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 24px;
  --spacing-xxl: 40px;

  --font-size-sm: 12px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 20px;

  --radius-sm: 10px;
  --radius-md: 14px;
  --radius-lg: 18px;

  --shadow-sm: 0 2px 8px rgba(15, 23, 42, 0.06);
  --shadow-md: 0 6px 20px rgba(15, 23, 42, 0.08);
  --shadow-lg: 0 14px 40px rgba(15, 23, 42, 0.12);

  --transition-fast: 160ms ease;
  --transition-med: 220ms cubic-bezier(.2,.8,.2,1);
}

/* Base */
* { box-sizing: border-box; }
html, body { height: 100%; }
body {
  margin: 0;
  background: radial-gradient(1200px 800px at 50% -200px, #eef2ff 0%, transparent 55%) var(--color-bg);
  color: var(--color-text);
  font: 14px/1.55 ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, "Helvetica Neue", Arial, "Noto Sans", "Apple Color Emoji", "Segoe UI Emoji";
}

/* Top action button */
.run-button {
  appearance: none;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  background: linear-gradient(180deg, var(--color-primary), var(--color-primary-hover));
  color: #fff;
  padding: 12px 18px;
  font-weight: 600;
  letter-spacing: .2px;
  box-shadow: var(--shadow-sm);
  cursor: pointer;
  transition: transform var(--transition-fast), box-shadow var(--transition-fast), filter var(--transition-fast);
  margin: var(--spacing-xl) auto 0;
  display: block;
}
.run-button:hover { filter: brightness(1.03); box-shadow: var(--shadow-md); transform: translateY(-1px); }
.run-button:active { transform: translateY(0); }
.run-button:focus-visible {
  outline: none;
  box-shadow: 0 0 0 4px var(--color-ring);
}

/* Cards container */
.cards-container{
  max-width: 1100px;
  margin: var(--spacing-xl) auto var(--spacing-xxl);
  padding: 0 var(--spacing-xl);
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: var(--spacing-xl);
}

.sessions-container{
  display: flex;
  flex-direction: row;

}
/* Card */
.tool-card{
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-xl);
  display: grid;
  align-content: start;
  gap: var(--spacing-lg);
  transition: transform var(--transition-med), box-shadow var(--transition-med), border-color var(--transition-fast);
}
.tool-card:hover{
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
  border-color: #dbe3f0;
}

/* Card content */
.tool-card .title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  letter-spacing: .2px;
}
.tool-card .meta {
  color: var(--color-text-secondary);
  font-size: var(--font-size-md);
}

/* Buttons inside card */
.tool-card button,
.refresh-button {
  appearance: none;
  border: 1px solid var(--color-border);
  background: #fff;
  color: var(--color-text);
  border-radius: var(--radius-sm);
  padding: 10px 12px;
  font-weight: 600;
  cursor: pointer;
  transition: background var(--transition-fast), transform var(--transition-fast), box-shadow var(--transition-fast), border-color var(--transition-fast);
}
.tool-card button:hover,
.refresh-button:hover {
  background: #f8fafc;
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
  border-color: #dbe3f0;
}
.tool-card button:focus-visible,
.refresh-button:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px var(--color-ring);
}

/* Small badge (optional if you add one) */
.badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  background: #f1f5f9;
  border: 1px solid var(--color-border);
  border-radius: 999px;
  padding: 6px 10px;
}

/* Responsive tweaks */
@media (max-width: 520px){
  .run-button { width: calc(100% - 32px); }
  .cards-container { padding: 0 var(--spacing-lg); gap: var(--spacing-lg); }
}

  #fileInput {
    display: none;
  }

  /* Style the label as a button */
  .file-label {
    display: inline-block;
    padding: 10px 20px;
    background-color: #4CAF50; /* green */
    color: white;
    font-size: 14px;
    font-weight: bold;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .file-label:hover {
    background-color: #45a049;
  }